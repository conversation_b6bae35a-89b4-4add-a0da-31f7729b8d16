import { describe, it, expect } from 'vitest';
import { encryptPassword, hashPassword, verifyPassword, validatePasswordStrength } from '../crypto.js';

describe('Crypto Integration Tests', () => {
  describe('Password Encryption Flow', () => {
    it('should encrypt passwords consistently', async () => {
      const password = 'TestPassword123!';
      
      // Encrypt the same password multiple times
      const encrypted1 = await encryptPassword(password);
      const encrypted2 = await encryptPassword(password);
      
      // Should produce different hashes due to timestamp-based salt
      expect(encrypted1).not.toBe(encrypted2);
      expect(typeof encrypted1).toBe('string');
      expect(typeof encrypted2).toBe('string');
      expect(encrypted1.length).toBeGreaterThan(0);
      expect(encrypted2.length).toBeGreaterThan(0);
    });

    it('should handle different password lengths', async () => {
      const shortPassword = 'Short1!';
      const longPassword = 'ThisIsAVeryLongPasswordWithManyCharacters123!@#$%^&*()';
      
      const encryptedShort = await encryptPassword(shortPassword);
      const encryptedLong = await encryptPassword(longPassword);
      
      expect(typeof encryptedShort).toBe('string');
      expect(typeof encryptedLong).toBe('string');
      expect(encryptedShort).not.toBe(encryptedLong);
    });

    it('should handle special characters in passwords', async () => {
      const specialPassword = '!@#$%^&*()_+-=[]{}|;:,.<>?`~';
      
      const encrypted = await encryptPassword(specialPassword);
      
      expect(typeof encrypted).toBe('string');
      expect(encrypted.length).toBeGreaterThan(0);
    });
  });

  describe('Password Hashing and Verification', () => {
    it('should hash and verify passwords correctly', async () => {
      const password = 'TestPassword123!';
      
      // Hash the password
      const { hash, salt } = await hashPassword(password);
      
      // Verify with correct password
      const isValidCorrect = await verifyPassword(password, hash, salt);
      expect(isValidCorrect).toBe(true);
      
      // Verify with incorrect password
      const isValidIncorrect = await verifyPassword('WrongPassword123!', hash, salt);
      expect(isValidIncorrect).toBe(false);
    });

    it('should generate different hashes for same password with different salts', async () => {
      const password = 'TestPassword123!';
      
      const result1 = await hashPassword(password);
      const result2 = await hashPassword(password);
      
      // Different salts should produce different hashes
      expect(result1.salt).not.toBe(result2.salt);
      expect(result1.hash).not.toBe(result2.hash);
    });

    it('should produce same hash for same password and salt', async () => {
      const password = 'TestPassword123!';
      const salt = 'fixedSalt123';
      
      const result1 = await hashPassword(password, salt);
      const result2 = await hashPassword(password, salt);
      
      expect(result1.hash).toBe(result2.hash);
      expect(result1.salt).toBe(salt);
      expect(result2.salt).toBe(salt);
    });
  });

  describe('Password Strength Validation', () => {
    it('should validate various password strengths correctly', () => {
      const testCases = [
        {
          password: 'StrongPassword123!',
          expected: { isValid: true, strength: 'strong', score: 5 }
        },
        {
          password: 'MediumPass123',
          expected: { isValid: false, strength: 'strong', score: 4 }
        },
        {
          password: 'weakpass',
          expected: { isValid: false, strength: 'weak', score: 2 }
        },
        {
          password: '123',
          expected: { isValid: false, strength: 'weak', score: 1 }
        },
        {
          password: '',
          expected: { isValid: false, strength: 'weak', score: 0 }
        }
      ];

      testCases.forEach(({ password, expected }) => {
        const result = validatePasswordStrength(password);
        expect(result.isValid).toBe(expected.isValid);
        expect(result.strength).toBe(expected.strength);
        expect(result.score).toBe(expected.score);
      });
    });

    it('should validate all password requirements individually', () => {
      const result = validatePasswordStrength('TestPassword123!');
      
      expect(result.minLength).toBe(true);
      expect(result.hasUpperCase).toBe(true);
      expect(result.hasLowerCase).toBe(true);
      expect(result.hasNumbers).toBe(true);
      expect(result.hasSpecialChar).toBe(true);
    });
  });

  describe('Real-world Usage Scenarios', () => {
    it('should handle typical user registration flow', async () => {
      const email = '<EMAIL>';
      const password = 'UserPassword123!';
      const confirmPassword = 'UserPassword123!';
      
      // Validate password strength
      const strength = validatePasswordStrength(password);
      expect(strength.isValid).toBe(true);
      
      // Check passwords match
      expect(password).toBe(confirmPassword);
      
      // Encrypt for transmission
      const encryptedPassword = await encryptPassword(password);
      const encryptedConfirmPassword = await encryptPassword(confirmPassword);
      
      expect(typeof encryptedPassword).toBe('string');
      expect(typeof encryptedConfirmPassword).toBe('string');
      expect(encryptedPassword.length).toBeGreaterThan(0);
      expect(encryptedConfirmPassword.length).toBeGreaterThan(0);
    });

    it('should handle typical login flow', async () => {
      const password = 'LoginPassword123!';
      
      // Encrypt password for transmission (as would happen in login)
      const encryptedPassword = await encryptPassword(password);
      
      expect(typeof encryptedPassword).toBe('string');
      expect(encryptedPassword.length).toBeGreaterThan(0);
      
      // Verify the original password is not exposed
      expect(encryptedPassword).not.toContain(password);
    });
  });

  describe('Security Properties', () => {
    it('should not expose original password in encrypted form', async () => {
      const password = 'SecretPassword123!';
      const encrypted = await encryptPassword(password);
      
      // The encrypted form should not contain the original password
      expect(encrypted).not.toContain(password);
      expect(encrypted).not.toContain('Secret');
      expect(encrypted).not.toContain('Password');
      expect(encrypted).not.toContain('123');
    });

    it('should produce different outputs for similar passwords', async () => {
      const password1 = 'Password123!';
      const password2 = 'Password124!';
      
      const encrypted1 = await encryptPassword(password1);
      const encrypted2 = await encryptPassword(password2);
      
      expect(encrypted1).not.toBe(encrypted2);
    });
  });
});
