/**
 * Cryptographic utilities for password encryption and security
 * Uses Web Crypto API for secure client-side encryption
 */

/**
 * Convert string to <PERSON>rrayBuffer
 * @param {string} str - String to convert
 * @returns {ArrayBuffer} ArrayBuffer representation
 */
function stringToArrayBuffer(str) {
  const encoder = new TextEncoder();
  return encoder.encode(str);
}

/**
 * Convert ArrayBuffer to hex string
 * @param {ArrayBuffer} buffer - ArrayBuffer to convert
 * @returns {string} Hex string representation
 */
function arrayBufferToHex(buffer) {
  const byteArray = new Uint8Array(buffer);
  const hexCodes = [...byteArray].map(value => {
    const hexCode = value.toString(16);
    const paddedHexCode = hexCode.padStart(2, '0');
    return paddedHexCode;
  });
  return hexCodes.join('');
}

/**
 * Generate a secure random salt
 * @param {number} length - Length of salt in bytes (default: 16)
 * @returns {string} Hex-encoded salt
 */
export function generateSalt(length = 16) {
  const array = new Uint8Array(length);
  crypto.getRandomValues(array);
  return arrayBufferToHex(array);
}

/**
 * Hash password using SHA-256 with salt
 * @param {string} password - Plain text password
 * @param {string} salt - Salt for hashing (optional, will generate if not provided)
 * @returns {Promise<{hash: string, salt: string}>} Hashed password and salt
 */
export async function hashPassword(password, salt = null) {
  try {
    // Generate salt if not provided
    if (!salt) {
      salt = generateSalt();
    }

    // Combine password and salt
    const saltedPassword = password + salt;
    
    // Convert to ArrayBuffer
    const data = stringToArrayBuffer(saltedPassword);
    
    // Hash using SHA-256
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    
    // Convert to hex string
    const hash = arrayBufferToHex(hashBuffer);
    
    return {
      hash,
      salt
    };
  } catch (error) {
    console.error('Password hashing failed:', error);
    throw new Error('Failed to hash password');
  }
}

/**
 * Encrypt password for secure transmission
 * This creates a one-way hash suitable for API transmission
 * @param {string} password - Plain text password
 * @returns {Promise<string>} Encrypted password hash
 */
export async function encryptPassword(password) {
  try {
    // Generate a timestamp-based salt for uniqueness
    const timestamp = Date.now().toString();
    const randomSalt = generateSalt(8);
    const transmissionSalt = timestamp + randomSalt;
    
    // Hash the password with the transmission salt
    const result = await hashPassword(password, transmissionSalt);
    
    // Return the hash (salt is not needed for transmission)
    return result.hash;
  } catch (error) {
    console.error('Password encryption failed:', error);
    throw new Error('Failed to encrypt password');
  }
}

/**
 * Verify password against hash
 * @param {string} password - Plain text password to verify
 * @param {string} hash - Stored hash to verify against
 * @param {string} salt - Salt used for original hash
 * @returns {Promise<boolean>} True if password matches
 */
export async function verifyPassword(password, hash, salt) {
  try {
    const result = await hashPassword(password, salt);
    return result.hash === hash;
  } catch (error) {
    console.error('Password verification failed:', error);
    return false;
  }
}

/**
 * Generate a secure random token
 * @param {number} length - Length in bytes (default: 32)
 * @returns {string} Hex-encoded random token
 */
export function generateSecureToken(length = 32) {
  const array = new Uint8Array(length);
  crypto.getRandomValues(array);
  return arrayBufferToHex(array);
}

/**
 * Validate password strength
 * @param {string} password - Password to validate
 * @returns {Object} Validation result with strength indicators
 */
export function validatePasswordStrength(password) {
  const minLength = password.length >= 8;
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
  
  const score = [minLength, hasUpperCase, hasLowerCase, hasNumbers, hasSpecialChar]
    .filter(Boolean).length;
  
  let strength = 'weak';
  if (score >= 4) strength = 'strong';
  else if (score >= 3) strength = 'medium';
  
  return {
    minLength,
    hasUpperCase,
    hasLowerCase,
    hasNumbers,
    hasSpecialChar,
    score,
    strength,
    isValid: minLength && hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChar
  };
}
